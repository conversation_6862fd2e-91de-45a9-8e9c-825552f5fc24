package com.ously.gamble.collectibles.dto;

import com.fasterxml.jackson.databind.JsonNode;
import com.ously.gamble.collectibles.persistence.model.Card;
import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.persistence.model.Reward;
import com.ously.gamble.collectibles.validation.EndDateAfterStartDate;
import com.ously.gamble.collectibles.validation.UniqueCollectionName;
import jakarta.validation.constraints.*;

import java.time.OffsetDateTime;
import java.time.OffsetDateTime;
import java.util.List;

public class CardCollectionDto {

    //---------------------
    // Request DTOs
    //---------------------

    @EndDateAfterStartDate
    public record CreateCardCollectionRequest(
            @NotBlank(message = "Collection name cannot be empty")
            @Size(max = 100, message = "Collection name cannot exceed 100 characters")
            String name,

            OffsetDateTime startDate,

            OffsetDateTime endDate,

            @Min(value = 0, message = "Sort order cannot be negative")
            @Max(value = 99, message = "Sort order cannot exceed 99")
            Integer sortOrder
    ) {}

    @EndDateAfterStartDate
    public record UpdateCardCollectionRequest(
            @Size(max = 100, message = "Collection name cannot exceed 100 characters")
            String name,

            OffsetDateTime startDate,
            OffsetDateTime endDate,
            CardCollection.CollectionStatus status,

            @Min(value = 0, message = "Sort order cannot be negative")
            @Max(value = 99, message = "Sort order cannot exceed 99")
            Integer sortOrder
    ) {}

    public record CreateCardRequest(
            @NotBlank(message = "Card name cannot be empty")
            @Size(max = 100, message = "Card name cannot exceed 100 characters")
            String name,

            @NotBlank(message = "Image URL is required")
            @Pattern(regexp = "^https?://.*", message = "Image URL must start with http:// or https://")
            String imageUrl,

            @NotNull(message = "Card pieces are required")
            @Size(min = 4, max = 4, message = "Card must have exactly 4 pieces")
            List<@Valid CreateCardPieceRequest> cardPieces,

            @Min(value = 0, message = "Sort order cannot be negative")
            @Max(value = 99, message = "Sort order cannot exceed 99")
            Byte sortOrder
    ) {}

    public record UpdateCardRequest(
            Integer id,

            @Size(max = 100, message = "Card name cannot exceed 100 characters")
            String name,

            @Pattern(regexp = "^https?://.*", message = "Image URL must start with http:// or https://")
            String imageUrl,

            @Size(min = 4, max = 4, message = "Card must have exactly 4 pieces")
            List<@Valid UpdateCardPieceRequest> cardPieces,

            Card.CardStatus status,

            @Min(value = 0, message = "Sort order cannot be negative")
            @Max(value = 99, message = "Sort order cannot exceed 99")
            Byte sortOrder
    ) {}

    public record CreateCardPieceRequest(
            @NotNull(message = "Piece number is required")
            @Min(value = 1, message = "Piece number must be between 1 and 4")
            @Max(value = 4, message = "Piece number must be between 1 and 4")
            Integer pieceNumber,

            @NotNull(message = "Rarity level is required")
            @Min(value = 1, message = "Rarity level must be between 1 and 3")
            @Max(value = 3, message = "Rarity level must be between 1 and 3")
            Integer rarityLevel
    ) {}

    public record UpdateCardPieceRequest(
            @NotNull(message = "Piece number is required")
            @Min(value = 1, message = "Piece number must be between 1 and 4")
            @Max(value = 4, message = "Piece number must be between 1 and 4")
            Integer pieceNumber,

            @NotNull(message = "Rarity level is required")
            @Min(value = 1, message = "Rarity level must be between 1 and 3")
            @Max(value = 3, message = "Rarity level must be between 1 and 3")
            Integer rarityLevel
    ) {}

    public record CreateRewardRequest(
            Integer collectionId,
            Integer cardId,

            @NotNull(message = "Reward type is required")
            Reward.RewardType rewardType,

            @Min(value = 1, message = "Milestone percentage must be between 1 and 100")
            @Max(value = 100, message = "Milestone percentage must be between 1 and 100")
            Byte milestonePercentage,

            @NotNull(message = "Reward data is required")
            JsonNode rewardData
    ) {
        public CreateRewardRequest {
            if (rewardType == Reward.RewardType.MILESTONE && milestonePercentage == null) {
                throw new IllegalArgumentException("Milestone percentage is required for milestone rewards");
            }
            if (rewardType == Reward.RewardType.COMPLETION && milestonePercentage != null) {
                throw new IllegalArgumentException("Milestone percentage should not be specified for completion rewards");
            }
        }
    }

    public record UpdateRewardRequest(
            @Min(value = 1, message = "Milestone percentage must be between 1 and 100")
            @Max(value = 100, message = "Milestone percentage must be between 1 and 100")
            Byte milestonePercentage,

            JsonNode rewardData
    ) {}

    //---------------------
    // Response DTOs
    //---------------------

    public record CardCollectionSummaryResponse(
            Integer id,
            String name,
            OffsetDateTime startDate,
            OffsetDateTime endDate,
            CardCollection.CollectionStatus status,
            Integer sortOrder
    ) {}



    public record CardResponse(
            Integer id,
            String name,
            String imageUrl,
            Byte rarityLevel,
            Card.CardStatus status,
            Byte sortOrder,
            List<CardPieceResponse> cardPieces,
            List<RewardResponse> rewards
    ) {}

    public record CardPieceResponse(
            Integer pieceNumber,
            Integer rarityLevel
    ) {}

    public record RewardResponse(
            Integer id,
            Reward.RewardType rewardType,
            Byte milestonePercentage,
            JsonNode rewardData,
            String targetType,
            Integer targetId,
            String targetName
    ) {}
}
